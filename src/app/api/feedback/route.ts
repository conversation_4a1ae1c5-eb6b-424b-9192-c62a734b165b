import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { aiAgentFeedback } from '@/lib/db/schema';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { matchId, feedbackType, rating, aspectRated, feedbackText } = await request.json();
    
    const feedback = await db.insert(aiAgentFeedback).values({
      matchId,
      userId: user.id,
      feedbackType,
      rating,
      aspectRated,
      feedbackText,
    }).returning();
    
    return NextResponse.json({ feedback: feedback[0] });
  } catch (error) {
    console.error('Error saving feedback:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
