import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { MatchingService } from '@/lib/services/matching';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const generateDaily = url.searchParams.get('generate_daily') === 'true';

    if (generateDaily) {
      // 生成单个新匹配（减少 token 消耗）
      try {
        const newMatch = await MatchingService.generateSingleMatch(user.id);
        if (newMatch) {
          // 返回包含新匹配的所有匹配
          const allMatches = await MatchingService.getUserMatches(user.id);
          return NextResponse.json({ matches: allMatches });
        } else {
          // 没有更多潜在匹配
          const matches = await MatchingService.getUserMatches(user.id);
          return NextResponse.json({ matches, message: '暂时没有新的匹配对象' });
        }
      } catch (error: any) {
        console.error('Error generating new match:', error);

        if (error.message === 'DAILY_LIMIT_EXCEEDED') {
          const limitInfo = error.limitInfo;
          const resetTime = new Date(limitInfo.nextResetTime);
          const hoursUntilReset = Math.ceil((resetTime.getTime() - Date.now()) / (1000 * 60 * 60));

          return NextResponse.json({
            error: 'DAILY_LIMIT_EXCEEDED',
            message: `今日匹配次数已达上限（3次）`,
            limitInfo: {
              remainingMatches: limitInfo.remainingMatches,
              hoursUntilReset: Math.max(1, hoursUntilReset),
              nextResetTime: limitInfo.nextResetTime
            }
          }, { status: 429 });
        }

        // 如果生成失败，返回现有匹配
        const matches = await MatchingService.getUserMatches(user.id);
        return NextResponse.json({ matches, error: '生成匹配失败，请稍后重试' });
      }
    } else {
      // 获取现有匹配
      const matches = await MatchingService.getUserMatches(user.id);
      return NextResponse.json({ matches });
    }
  } catch (error) {
    console.error('Error fetching matches:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action, targetUserId } = await request.json();
    
    if (action === 'generate_daily') {
      const dailyMatches = await MatchingService.generateDailyMatches(user.id);
      return NextResponse.json({ matches: dailyMatches });
    }
    
    if (action === 'create_match' && targetUserId) {
      const match = await MatchingService.createMatch(user.id, targetUserId);
      return NextResponse.json({ match });
    }
    
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error processing match request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
