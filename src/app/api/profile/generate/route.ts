import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { GeminiService } from '@/lib/services/gemini';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name, age, gender, location, interests } = await request.json();

    // 验证必要字段
    if (!name || !age || !gender) {
      return NextResponse.json({ 
        error: '请先填写姓名、年龄和性别信息' 
      }, { status: 400 });
    }

    // 生成AI个人资料
    const generatedProfile = await GeminiService.generateProfile({
      name,
      age,
      gender,
      location,
      interests
    });

    return NextResponse.json(generatedProfile);
  } catch (error) {
    console.error('Error generating profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
