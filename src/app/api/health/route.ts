import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const startTime = Date.now();
    
    // 检查数据库连接
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }

    // 检查环境变量
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'OPENROUTER_API_KEY'
    ];

    const missingVars = requiredEnvVars.filter(
      varName => !process.env[varName]
    );

    // 检查 OpenRouter API 连接（简单测试）
    let openRouterStatus = 'unknown';
    try {
      if (process.env.OPENROUTER_API_KEY) {
        // 这里可以添加一个简单的 API 测试
        openRouterStatus = 'configured';
      } else {
        openRouterStatus = 'not_configured';
      }
    } catch (error) {
      openRouterStatus = 'error';
    }

    const responseTime = Date.now() - startTime;

    const healthData: any = {
      status: missingVars.length === 0 ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      services: {
        database: 'connected',
        openrouter: openRouterStatus,
      },
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        vercelEnv: process.env.VERCEL_ENV || 'development',
        region: process.env.VERCEL_REGION || 'unknown',
      },
      version: process.env.npm_package_version || '1.0.0',
      deployment: {
        vercelUrl: process.env.VERCEL_URL || 'localhost',
        gitCommitSha: process.env.VERCEL_GIT_COMMIT_SHA?.substring(0, 7) || 'unknown',
        gitBranch: process.env.VERCEL_GIT_COMMIT_REF || 'unknown',
      }
    };

    if (missingVars.length > 0) {
      healthData.warnings = {
        missingEnvVars: missingVars
      };
    }

    return NextResponse.json(healthData, {
      status: missingVars.length === 0 ? 200 : 206,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      services: {
        database: 'error',
        openrouter: 'unknown',
      },
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        vercelEnv: process.env.VERCEL_ENV || 'development',
      }
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}
