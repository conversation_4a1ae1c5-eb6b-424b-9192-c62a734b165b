import { db } from '@/lib/db';
import { users, userProfiles, matches, type User, type UserProfile, type NewMatch } from '@/lib/db/schema';
import { eq, and, or, ne, gte } from 'drizzle-orm';
import { GeminiService } from './gemini';

export class MatchingService {
  // 检查24小时内的匹配次数
  static async checkDailyMatchLimit(userId: string): Promise<{ canMatch: boolean; remainingMatches: number; nextResetTime: Date }> {
    try {
      // 计算24小时前的时间
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      // 查询24小时内用户创建的匹配数量
      const recentMatches = await db
        .select()
        .from(matches)
        .where(
          and(
            eq(matches.user1Id, userId),
            gte(matches.createdAt, twentyFourHoursAgo)
          )
        );

      const matchCount = recentMatches.length;
      const maxDailyMatches = 3;
      const canMatch = matchCount < maxDailyMatches;
      const remainingMatches = Math.max(0, maxDailyMatches - matchCount);

      // 计算下次重置时间（最早匹配的24小时后）
      let nextResetTime = new Date();
      if (recentMatches.length > 0) {
        const earliestMatch = recentMatches.reduce((earliest, match) =>
          new Date(match.createdAt || new Date()) < new Date(earliest.createdAt || new Date()) ? match : earliest
        );
        nextResetTime = new Date(earliestMatch.createdAt || new Date());
        nextResetTime.setHours(nextResetTime.getHours() + 24);
      }

      return {
        canMatch,
        remainingMatches,
        nextResetTime
      };
    } catch (error) {
      console.error('Error checking daily match limit:', error);
      // 如果检查失败，默认允许匹配
      return {
        canMatch: true,
        remainingMatches: 3,
        nextResetTime: new Date()
      };
    }
  }

  static async findPotentialMatches(userId: string, limit: number = 10): Promise<User[]> {
    // Get current user's profile
    const currentUser = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser.length) {
      throw new Error('User not found');
    }

    // Get all existing matches for current user (both as user1 and user2)
    const existingMatches = await db
      .select()
      .from(matches)
      .where(
        or(
          eq(matches.user1Id, userId),
          eq(matches.user2Id, userId)
        )
      );

    // Extract all matched user IDs
    const existingMatchIds = new Set<string>();
    existingMatches.forEach((match: any) => {
      if (match.user1Id === userId) {
        existingMatchIds.add(match.user2Id);
      } else {
        existingMatchIds.add(match.user1Id);
      }
    });

    // Get current user's gender for opposite gender matching
    const currentUserGender = currentUser[0].gender;
    const targetGender = currentUserGender === 'male' ? 'female' : 'male';

    // Find all potential matches (excluding current user, already matched users, and same gender)
    const allPotentialMatches = await db
      .select()
      .from(users)
      .where(
        and(
          ne(users.id, userId),
          eq(users.isActive, true),
          eq(users.gender, targetGender) // Only match opposite gender
        )
      );

    // Filter out already matched users
    const availableMatches = allPotentialMatches.filter((user: any) =>
      !existingMatchIds.has(user.id)
    );

    // Randomize the results to avoid always matching the same users
    const shuffled = availableMatches.sort(() => Math.random() - 0.5);

    // Return limited number of matches
    return shuffled.slice(0, limit);
  }

  static async createMatch(user1Id: string, user2Id: string): Promise<any> {
    try {
      // Get both users' profiles
      const [user1Data, user2Data] = await Promise.all([
        this.getUserWithProfile(user1Id),
        this.getUserWithProfile(user2Id),
      ]);

      if (!user1Data || !user2Data) {
        throw new Error('One or both users not found');
      }

      // Generate AI analysis
      const [personalitySummary1, personalitySummary2] = await Promise.all([
        GeminiService.generatePersonalitySummary({
          ...user1Data.user,
          ...user1Data.profile,
        }),
        GeminiService.generatePersonalitySummary({
          ...user2Data.user,
          ...user2Data.profile,
        }),
      ]);

      // Calculate compatibility score
      const compatibilityScore = await GeminiService.calculateCompatibilityScore(
        { ...user1Data.user, ...user1Data.profile, personalitySummary: personalitySummary1 },
        { ...user2Data.user, ...user2Data.profile, personalitySummary: personalitySummary2 }
      );

      // Simulate conversation
      const conversationSimulation = await GeminiService.simulateConversation(
        { ...user1Data.user, ...user1Data.profile, personalitySummary: personalitySummary1 },
        { ...user2Data.user, ...user2Data.profile, personalitySummary: personalitySummary2 }
      );

      // Generate comprehensive match analysis
      const matchAnalysis = await GeminiService.generateComprehensiveAnalysis(
        { ...user1Data.user, ...user1Data.profile },
        { ...user2Data.user, ...user2Data.profile },
        compatibilityScore
      );

      // Generate date plan
      const datePlan = await GeminiService.generateDatePlan(
        { ...user1Data.user, ...user1Data.profile },
        { ...user2Data.user, ...user2Data.profile }
      );

      // Create match record
      const matchData: NewMatch = {
        user1Id,
        user2Id,
        compatibilityScore,
        aiAnalysis: {
          user1PersonalitySummary: personalitySummary1,
          user2PersonalitySummary: personalitySummary2,
          explanation: matchAnalysis.explanation,
          strengths: matchAnalysis.strengths,
          challenges: matchAnalysis.challenges,
          suggestions: matchAnalysis.suggestions,
          datePlan: datePlan,
        },
        conversationSimulation,
        status: 'pending',
      };

      const [match] = await db.insert(matches).values(matchData).returning();

      return {
        match,
        user1: user1Data.user,
        user2: user2Data.user,
        compatibilityScore,
        explanation: matchAnalysis.explanation,
        conversationSimulation,
      };
    } catch (error) {
      console.error('Error creating match:', error);
      throw error;
    }
  }

  static async getUserMatches(userId: string): Promise<any[]> {
    const userMatches = await db
      .select()
      .from(matches)
      .where(or(eq(matches.user1Id, userId), eq(matches.user2Id, userId)));

    const enrichedMatches = await Promise.all(
      userMatches.map(async (match: any) => {
        const otherUserId = match.user1Id === userId ? match.user2Id : match.user1Id;
        const otherUserData = await this.getUserWithProfile(otherUserId);
        
        return {
          ...match,
          otherUser: otherUserData?.user,
          otherUserProfile: otherUserData?.profile,
        };
      })
    );

    return enrichedMatches;
  }

  static async updateMatchStatus(matchId: string, userId: string, liked: boolean): Promise<any> {
    const [match] = await db
      .select()
      .from(matches)
      .where(eq(matches.id, matchId))
      .limit(1);

    if (!match) {
      throw new Error('Match not found');
    }

    const isUser1 = match.user1Id === userId;
    const updateData: any = {};

    if (isUser1) {
      updateData.user1Liked = liked;
      updateData.user1Viewed = true;
    } else {
      updateData.user2Liked = liked;
      updateData.user2Viewed = true;
    }

    // Check if both users have liked each other
    const otherUserLiked = isUser1 ? match.user2Liked : match.user1Liked;
    if (liked && otherUserLiked) {
      updateData.status = 'mutual_like';
    } else if (!liked) {
      updateData.status = 'rejected';
    }

    const [updatedMatch] = await db
      .update(matches)
      .set(updateData)
      .where(eq(matches.id, matchId))
      .returning();

    return updatedMatch;
  }

  private static async getUserWithProfile(userId: string): Promise<{ user: User; profile: UserProfile | null } | null> {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) return null;

    const [profile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, userId))
      .limit(1);

    return { user, profile: profile || null };
  }

  static async generateDailyMatches(userId: string): Promise<any[]> {
    try {
      // 改为一次只生成一个匹配，减少 token 消耗
      const potentialMatches = await this.findPotentialMatches(userId, 1);
      const matches = [];

      for (const potentialMatch of potentialMatches) {
        try {
          const match = await this.createMatch(userId, potentialMatch.id);
          matches.push(match);
        } catch (error) {
          console.error(`Error creating match with user ${potentialMatch.id}:`, error);
          // Continue with other matches even if one fails
        }
      }

      return matches;
    } catch (error) {
      console.error('Error generating daily matches:', error);
      throw error;
    }
  }

  static async generateSingleMatch(userId: string): Promise<any | null> {
    try {
      // 检查24小时内的匹配限制
      const limitCheck = await this.checkDailyMatchLimit(userId);
      if (!limitCheck.canMatch) {
        const error = new Error('DAILY_LIMIT_EXCEEDED');
        (error as any).limitInfo = limitCheck;
        throw error;
      }

      // Find one potential match for the user
      const potentialMatches = await this.findPotentialMatches(userId, 1);

      if (potentialMatches.length === 0) {
        return null; // No more potential matches
      }

      const match = await this.createMatch(userId, potentialMatches[0].id);
      return match;
    } catch (error) {
      console.error('Error generating single match:', error);
      throw error;
    }
  }
}
