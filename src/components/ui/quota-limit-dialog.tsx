'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Zap, Heart, X } from 'lucide-react';

interface QuotaLimitDialogProps {
  isOpen: boolean;
  onClose: () => void;
  hoursUntilReset: number;
  nextResetTime: string;
}

export function QuotaLimitDialog({ 
  isOpen, 
  onClose, 
  hoursUntilReset, 
  nextResetTime 
}: QuotaLimitDialogProps) {
  if (!isOpen) return null;

  const formatResetTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md mx-auto bg-white shadow-2xl">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-orange-600" />
              </div>
              <CardTitle className="text-xl font-bold text-gray-900 mb-2">
                今日匹配额度已用完
              </CardTitle>
              <CardDescription className="text-gray-600">
                为了控制AI分析成本，每24小时限制3次匹配
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 额度状态 */}
          <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Heart className="w-5 h-5 text-orange-600" />
                <span className="font-medium text-gray-800">匹配额度</span>
              </div>
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                3 / 3 已用完
              </Badge>
            </div>
            
            {/* 进度条 */}
            <div className="w-full bg-orange-200 rounded-full h-2">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full w-full"></div>
            </div>
          </div>

          {/* 重置时间 */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Clock className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <div className="font-medium text-gray-800">额度重置时间</div>
                <div className="text-sm text-gray-600">
                  大约 <span className="font-medium text-blue-600">{hoursUntilReset} 小时</span> 后
                </div>
                <div className="text-xs text-gray-500">
                  {formatResetTime(nextResetTime)}
                </div>
              </div>
            </div>
          </div>

          {/* 说明信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2">为什么有限制？</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• AI深度分析需要消耗大量计算资源</li>
              <li>• 限制使用可以控制运营成本</li>
              <li>• 确保每次匹配都是高质量的分析</li>
              <li>• 未来会推出付费额度功能</li>
            </ul>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={onClose}
            >
              我知道了
            </Button>
            <Button
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              onClick={onClose}
            >
              查看现有匹配
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
