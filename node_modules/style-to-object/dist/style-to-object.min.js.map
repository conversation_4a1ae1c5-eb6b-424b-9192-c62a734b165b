{"version": 3, "file": "style-to-object.min.js", "sources": ["../node_modules/inline-style-parser/index.js", "../src/index.ts"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", null], "names": ["COMMENT_REGEX", "NEWLINE_REGEX", "WHITESPACE_REGEX", "PROPERTY_REGEX", "COLON_REGEX", "VALUE_REGEX", "SEMICOLON_REGEX", "TRIM_REGEX", "EMPTY_STRING", "trim", "str", "replace", "inlineStyleParser", "style", "options", "TypeError", "lineno", "column", "updatePosition", "lines", "match", "length", "i", "lastIndexOf", "position", "start", "line", "node", "Position", "whitespace", "this", "end", "source", "error", "msg", "err", "Error", "reason", "filename", "silent", "re", "m", "exec", "slice", "comments", "rules", "c", "comment", "push", "pos", "char<PERSON>t", "type", "declaration", "prop", "val", "ret", "property", "value", "prototype", "content", "decl", "decls", "declarations", "iterator", "styleObject", "parse", "hasIterator", "for<PERSON>ach"], "mappings": "+XAEA,IAAIA,EAAgB,kCAEhBC,EAAgB,MAChBC,EAAmB,OAGnBC,EAAiB,yCACjBC,EAAc,QACdC,EAAc,uDACdC,EAAkB,UAGlBC,EAAa,aAMbC,EAAe,GA8OnB,SAASC,EAAKC,GACZ,OAAOA,EAAMA,EAAIC,QAAQJ,EAAYC,GAAgBA,CACvD,QAnOAI,EAAiB,SAAUC,EAAOC,GAChC,GAAqB,iBAAVD,EACT,MAAM,IAAIE,UAAU,mCAGtB,IAAKF,EAAO,MAAO,GAEnBC,EAAUA,GAAW,CAAE,EAKvB,IAAIE,EAAS,EACTC,EAAS,EAOb,SAASC,EAAeR,GACtB,IAAIS,EAAQT,EAAIU,MAAMnB,GAClBkB,IAAOH,GAAUG,EAAME,QAC3B,IAAIC,EAAIZ,EAAIa,YAvCF,MAwCVN,GAAUK,EAAIZ,EAAIW,OAASC,EAAIL,EAASP,EAAIW,MAChD,CAOE,SAASG,IACP,IAAIC,EAAQ,CAAEC,KAAMV,EAAQC,OAAQA,GACpC,OAAO,SAAUU,GAGf,OAFAA,EAAKH,SAAW,IAAII,EAASH,GAC7BI,IACOF,CACR,CACL,CAUE,SAASC,EAASH,GAChBK,KAAKL,MAAQA,EACbK,KAAKC,IAAM,CAAEL,KAAMV,EAAQC,OAAQA,GACnCa,KAAKE,OAASlB,EAAQkB,MAC1B,CAeE,SAASC,EAAMC,GACb,IAAIC,EAAM,IAAIC,MACZtB,EAAQkB,OAAS,IAAMhB,EAAS,IAAMC,EAAS,KAAOiB,GAQxD,GANAC,EAAIE,OAASH,EACbC,EAAIG,SAAWxB,EAAQkB,OACvBG,EAAIT,KAAOV,EACXmB,EAAIlB,OAASA,EACbkB,EAAIH,OAASnB,GAETC,EAAQyB,OAGV,MAAMJ,CAEZ,CAQE,SAASf,EAAMoB,GACb,IAAIC,EAAID,EAAGE,KAAK7B,GAChB,GAAK4B,EAAL,CACA,IAAI/B,EAAM+B,EAAE,GAGZ,OAFAvB,EAAeR,GACfG,EAAQA,EAAM8B,MAAMjC,EAAIW,QACjBoB,CAJC,CAKZ,CAKE,SAASZ,IACPT,EAAMlB,EACV,CAQE,SAAS0C,EAASC,GAChB,IAAIC,EAEJ,IADAD,EAAQA,GAAS,GACTC,EAAIC,MACA,IAAND,GACFD,EAAMG,KAAKF,GAGf,OAAOD,CACX,CAQE,SAASE,IACP,IAAIE,EAAMzB,IACV,GAnJgB,KAmJKX,EAAMqC,OAAO,IAlJvB,KAkJyCrC,EAAMqC,OAAO,GAAjE,CAGA,IADA,IAAI5B,EAAI,EAENd,GAAgBK,EAAMqC,OAAO5B,KAtJpB,KAuJIT,EAAMqC,OAAO5B,IAxJZ,KAwJmCT,EAAMqC,OAAO5B,EAAI,OAEhEA,EAIJ,GAFAA,GAAK,EAEDd,IAAiBK,EAAMqC,OAAO5B,EAAI,GACpC,OAAOW,EAAM,0BAGf,IAAIvB,EAAMG,EAAM8B,MAAM,EAAGrB,EAAI,GAM7B,OALAL,GAAU,EACVC,EAAeR,GACfG,EAAQA,EAAM8B,MAAMrB,GACpBL,GAAU,EAEHgC,EAAI,CACTE,KApKa,UAqKbJ,QAASrC,GAvB0D,CAyBzE,CAQE,SAAS0C,IACP,IAAIH,EAAMzB,IAGN6B,EAAOjC,EAAMjB,GACjB,GAAKkD,EAAL,CAIA,GAHAN,KAGK3B,EAAMhB,GAAc,OAAO6B,EAAM,wBAGtC,IAAIqB,EAAMlC,EAAMf,GAEZkD,EAAMN,EAAI,CACZE,KA7LiB,cA8LjBK,SAAU/C,EAAK4C,EAAK,GAAG1C,QAAQX,EAAeQ,IAC9CiD,MAAOH,EACH7C,EAAK6C,EAAI,GAAG3C,QAAQX,EAAeQ,IACnCA,IAMN,OAFAY,EAAMd,GAECiD,CApBI,CAqBf,CAyBE,OA9JA3B,EAAS8B,UAAUC,QAAU9C,EA6J7BgB,IAjBA,WACE,IAKI+B,EALAC,EAAQ,GAMZ,IAJAjB,EAASiB,GAIDD,EAAOR,MACA,IAATQ,IACFC,EAAMb,KAAKY,GACXhB,EAASiB,IAIb,OAAOA,CACX,CAGSC,EACR,aC7Na,SACZjD,EACAkD,GAEA,IAAIC,EAAkC,KAEtC,IAAKnD,GAA0B,iBAAVA,EACnB,OAAOmD,EAGT,IAAMF,EAAeG,EAAMpD,GACrBqD,EAAkC,mBAAbH,EAiB3B,OAfAD,EAAaK,SAAQ,SAACf,GACpB,GAAyB,gBAArBA,EAAYD,KAAhB,CAIQ,IAAAK,EAAoBJ,EAAWI,SAArBC,EAAUL,EAAWK,MAEnCS,EACFH,EAASP,EAAUC,EAAOL,GACjBK,KACTO,EAAcA,GAAe,CAAE,GACnBR,GAAYC,GAE5B,IAEOO,CACT", "x_google_ignoreList": [0]}