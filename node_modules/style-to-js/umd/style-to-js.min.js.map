{"version": 3, "file": "style-to-js.min.js", "sources": ["../node_modules/style-to-object/cjs/index.js", "../node_modules/inline-style-parser/index.js", "../cjs/utilities.js", "../cjs/index.js?commonjs-entry", "../cjs/index.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = StyleToObject;\nvar inline_style_parser_1 = __importDefault(require(\"inline-style-parser\"));\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nfunction StyleToObject(style, iterator) {\n    var styleObject = null;\n    if (!style || typeof style !== 'string') {\n        return styleObject;\n    }\n    var declarations = (0, inline_style_parser_1.default)(style);\n    var hasIterator = typeof iterator === 'function';\n    declarations.forEach(function (declaration) {\n        if (declaration.type !== 'declaration') {\n            return;\n        }\n        var property = declaration.property, value = declaration.value;\n        if (hasIterator) {\n            iterator(property, value, declaration);\n        }\n        else if (value) {\n            styleObject = styleObject || {};\n            styleObject[property] = value;\n        }\n    });\n    return styleObject;\n}\n//# sourceMappingURL=index.js.map", "// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */\nvar skipCamelCase = function (property) {\n    return !property ||\n        NO_HYPHEN_REGEX.test(property) ||\n        CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */\nvar capitalize = function (match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nvar trimHyphen = function (match, prefix) { return \"\".concat(prefix, \"-\"); };\n/**\n * CamelCases a CSS property.\n */\nvar camelCase = function (property, options) {\n    if (options === void 0) { options = {}; }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase;\n//# sourceMappingURL=utilities.js.map", "import { getDefaultExportFromCjs } from \"\u0000commonjsHelpers.js\";\nimport { __require as requireCjs } from \"/home/<USER>/work/style-to-js/style-to-js/cjs/index.js\";\nvar cjsExports = requireCjs();\nexport { cjsExports as __moduleExports };\nexport default /*@__PURE__*/getDefaultExportFromCjs(cjsExports);", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar style_to_object_1 = __importDefault(require(\"style-to-object\"));\nvar utilities_1 = require(\"./utilities\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== 'string') {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function (property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS;\n//# sourceMappingURL=index.js.map"], "names": ["__importDefault", "this", "mod", "__esModule", "default", "Object", "defineProperty", "cjs", "value", "style", "iterator", "styleObject", "declarations", "inline_style_parser_1", "hasIterator", "for<PERSON>ach", "declaration", "type", "property", "COMMENT_REGEX", "NEWLINE_REGEX", "WHITESPACE_REGEX", "PROPERTY_REGEX", "COLON_REGEX", "VALUE_REGEX", "SEMICOLON_REGEX", "TRIM_REGEX", "EMPTY_STRING", "trim", "str", "replace", "inlineStyleParser", "options", "TypeError", "lineno", "column", "updatePosition", "lines", "match", "length", "i", "lastIndexOf", "position", "start", "line", "node", "Position", "whitespace", "end", "source", "error", "msg", "err", "Error", "reason", "filename", "silent", "re", "m", "exec", "slice", "comments", "rules", "c", "comment", "push", "pos", "char<PERSON>t", "prop", "val", "ret", "prototype", "content", "decl", "decls", "require$$0", "utilities", "camelCase", "CUSTOM_PROPERTY_REGEX", "HYPHEN_REGEX", "NO_HYPHEN_REGEX", "VENDOR_PREFIX_REGEX", "MS_VENDOR_PREFIX_REGEX", "capitalize", "character", "toUpperCase", "trimHyphen", "prefix", "concat", "test", "skipCamelCase", "toLowerCase", "reactCompat", "getDefaultExportFromCjs", "style_to_object_1", "utilities_1", "require$$1", "StyleToJS", "output", "requireCjs"], "mappings": "4XACA,IAAIA,EAAmBC,GAAQA,EAAKD,iBAAoB,SAAUE,GAC9D,OAAQA,GAAOA,EAAIC,WAAcD,EAAM,CAAEE,QAAWF,EACvD,EACDG,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAAH,QAgBA,SAAuBK,EAAOC,GAC1B,IAAIC,EAAc,KAClB,IAAKF,GAA0B,iBAAVA,EACjB,OAAOE,EAEX,IAAIC,GAAe,EAAIC,EAAsBT,SAASK,GAClDK,EAAkC,mBAAbJ,EAczB,OAbAE,EAAaG,SAAQ,SAAUC,GAC3B,GAAyB,gBAArBA,EAAYC,KAAhB,CAGA,IAAIC,EAAWF,EAAYE,SAAUV,EAAQQ,EAAYR,MACrDM,EACAJ,EAASQ,EAAUV,EAAOQ,GAErBR,KACLG,EAAcA,GAAe,CAAE,GACnBO,GAAYV,EAPpC,CASA,IACWG,CACX,EApCA,IAAIE,EAAwBb,+BCJ5B,IAAImB,EAAgB,kCAEhBC,EAAgB,MAChBC,EAAmB,OAGnBC,EAAiB,yCACjBC,EAAc,QACdC,EAAc,uDACdC,EAAkB,UAGlBC,EAAa,aAMbC,EAAe,GA8OnB,SAASC,EAAKC,GACZ,OAAOA,EAAMA,EAAIC,QAAQJ,EAAYC,GAAgBA,CACvD,QAnOAI,EAAiB,SAAUtB,EAAOuB,GAChC,GAAqB,iBAAVvB,EACT,MAAM,IAAIwB,UAAU,mCAGtB,IAAKxB,EAAO,MAAO,GAEnBuB,EAAUA,GAAW,CAAE,EAKvB,IAAIE,EAAS,EACTC,EAAS,EAOb,SAASC,EAAeP,GACtB,IAAIQ,EAAQR,EAAIS,MAAMlB,GAClBiB,IAAOH,GAAUG,EAAME,QAC3B,IAAIC,EAAIX,EAAIY,YAvCF,MAwCVN,GAAUK,EAAIX,EAAIU,OAASC,EAAIL,EAASN,EAAIU,MAChD,CAOE,SAASG,IACP,IAAIC,EAAQ,CAAEC,KAAMV,EAAQC,OAAQA,GACpC,OAAO,SAAUU,GAGf,OAFAA,EAAKH,SAAW,IAAII,EAASH,GAC7BI,IACOF,CACR,CACL,CAUE,SAASC,EAASH,GAChB1C,KAAK0C,MAAQA,EACb1C,KAAK+C,IAAM,CAAEJ,KAAMV,EAAQC,OAAQA,GACnClC,KAAKgD,OAASjB,EAAQiB,MAC1B,CAeE,SAASC,EAAMC,GACb,IAAIC,EAAM,IAAIC,MACZrB,EAAQiB,OAAS,IAAMf,EAAS,IAAMC,EAAS,KAAOgB,GAQxD,GANAC,EAAIE,OAASH,EACbC,EAAIG,SAAWvB,EAAQiB,OACvBG,EAAIR,KAAOV,EACXkB,EAAIjB,OAASA,EACbiB,EAAIH,OAASxC,GAETuB,EAAQwB,OAGV,MAAMJ,CAEZ,CAQE,SAASd,EAAMmB,GACb,IAAIC,EAAID,EAAGE,KAAKlD,GAChB,GAAKiD,EAAL,CACA,IAAI7B,EAAM6B,EAAE,GAGZ,OAFAtB,EAAeP,GACfpB,EAAQA,EAAMmD,MAAM/B,EAAIU,QACjBmB,CAJC,CAKZ,CAKE,SAASX,IACPT,EAAMjB,EACV,CAQE,SAASwC,EAASC,GAChB,IAAIC,EAEJ,IADAD,EAAQA,GAAS,GACTC,EAAIC,MACA,IAAND,GACFD,EAAMG,KAAKF,GAGf,OAAOD,CACX,CAQE,SAASE,IACP,IAAIE,EAAMxB,IACV,GAnJgB,KAmJKjC,EAAM0D,OAAO,IAlJvB,KAkJyC1D,EAAM0D,OAAO,GAAjE,CAGA,IADA,IAAI3B,EAAI,EAENb,GAAgBlB,EAAM0D,OAAO3B,KAtJpB,KAuJI/B,EAAM0D,OAAO3B,IAxJZ,KAwJmC/B,EAAM0D,OAAO3B,EAAI,OAEhEA,EAIJ,GAFAA,GAAK,EAEDb,IAAiBlB,EAAM0D,OAAO3B,EAAI,GACpC,OAAOU,EAAM,0BAGf,IAAIrB,EAAMpB,EAAMmD,MAAM,EAAGpB,EAAI,GAM7B,OALAL,GAAU,EACVC,EAAeP,GACfpB,EAAQA,EAAMmD,MAAMpB,GACpBL,GAAU,EAEH+B,EAAI,CACTjD,KApKa,UAqKb+C,QAASnC,GAvB0D,CAyBzE,CAQE,SAASb,IACP,IAAIkD,EAAMxB,IAGN0B,EAAO9B,EAAMhB,GACjB,GAAK8C,EAAL,CAIA,GAHAJ,KAGK1B,EAAMf,GAAc,OAAO2B,EAAM,wBAGtC,IAAImB,EAAM/B,EAAMd,GAEZ8C,EAAMJ,EAAI,CACZjD,KA7LiB,cA8LjBC,SAAUU,EAAKwC,EAAK,GAAGtC,QAAQX,EAAeQ,IAC9CnB,MAAO6D,EACHzC,EAAKyC,EAAI,GAAGvC,QAAQX,EAAeQ,IACnCA,IAMN,OAFAW,EAAMb,GAEC6C,CApBI,CAqBf,CAyBE,OA9JAxB,EAASyB,UAAUC,QAAU/D,EA6J7BsC,IAjBA,WACE,IAKI0B,EALAC,EAAQ,GAMZ,IAJAb,EAASa,GAIDD,EAAOzD,MACA,IAATyD,IACFC,EAAMT,KAAKQ,GACXZ,EAASa,IAIb,OAAOA,CACX,CAGS9D,EACR,EDpP2C+D,2DEL5CtE,OAAOC,eAAesE,EAAS,aAAc,CAAEpE,OAAO,IACrCoE,EAAAC,eAAG,EACpB,IAAIC,EAAwB,qBACxBC,EAAe,YACfC,EAAkB,UAClBC,EAAsB,6BACtBC,EAAyB,UAYzBC,EAAa,SAAU7C,EAAO8C,GAC9B,OAAOA,EAAUC,aACpB,EAIGC,EAAa,SAAUhD,EAAOiD,GAAU,MAAO,GAAGC,OAAOD,EAAQ,IAAO,SAoB5EX,EAAAC,UAhBgB,SAAU3D,EAAUc,GAEhC,YADgB,IAAZA,IAAsBA,EAAU,CAAA,GAnBpB,SAAUd,GAC1B,OAAQA,GACJ8D,EAAgBS,KAAKvE,IACrB4D,EAAsBW,KAAKvE,EAClC,CAgBOwE,CAAcxE,GACPA,GAEXA,EAAWA,EAASyE,eAGhBzE,EAFAc,EAAQ4D,YAEG1E,EAASY,QAAQoD,EAAwBI,GAIzCpE,EAASY,QAAQmD,EAAqBK,IAErCxD,QAAQiD,EAAcI,GACzC,WCxC2BU,+BCH5B,IAGIC,GAHmB7F,GAAQA,EAAKD,iBAAoB,SAAUE,GAC9D,OAAQA,GAAOA,EAAIC,WAAcD,EAAM,CAAEE,QAAWF,EACvD,GACuCyE,KACpCoB,EAAcC,IAIlB,SAASC,EAAUxF,EAAOuB,GACtB,IAAIkE,EAAS,CAAE,EACf,OAAKzF,GAA0B,iBAAVA,IAGrB,EAAIqF,EAAkB1F,SAASK,GAAO,SAAUS,EAAUV,GAElDU,GAAYV,IACZ0F,GAAO,EAAIH,EAAYlB,WAAW3D,EAAUc,IAAYxB,EAEpE,IACW0F,GARIA,CASf,QACAD,EAAU7F,QAAU6F,EACpB1F,EAAiB0F,EDrBAE", "x_google_ignoreList": [0, 1]}