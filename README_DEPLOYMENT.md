# 🚀 GuaLao AI - Vercel 部署快速指南

这是一个简化的部署指南，帮助您快速将 GuaLao AI 交友应用部署到 Vercel。

## 📦 部署文件概览

我已经为您准备了完整的部署配置：

```
📁 部署相关文件
├── 📄 DEPLOYMENT.md              # 详细部署文档
├── 📄 DEPLOYMENT_CHECKLIST.md    # 部署检查清单
├── 📄 vercel.json                # Vercel 配置文件
├── 📄 .env.example               # 环境变量模板
├── 📄 README_DEPLOYMENT.md       # 本文件（快速指南）
├── 📁 scripts/
│   ├── 📄 check-env.js           # 环境变量检查脚本
│   ├── 📄 deploy.sh              # 自动部署脚本
│   └── 📄 seed-users-new.ts      # 扩展的用户数据生成脚本
└── 📁 src/app/api/
    └── 📄 health/route.ts         # 健康检查 API
```

## ⚡ 快速部署步骤

### 1. 环境变量配置

在 Vercel Dashboard 中设置以下环境变量：

```bash
# 必需的环境变量
NEXT_PUBLIC_SUPABASE_URL=https://hgwftsuazcmgemuxxpue.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
OPENROUTER_API_KEY=sk-or-v1-...
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=https://your-app-name.vercel.app
```

### 2. 代码修复（重要！）

在部署前需要修复 cookies API 警告：

**需要修改的文件：**
- `src/app/api/auth/sync/route.ts`
- `src/app/api/profile/route.ts` 
- `src/app/api/profile/generate/route.ts`
- `src/app/api/matches/route.ts`
- `src/app/api/matches/[id]/route.ts`

**修改方式：**
```typescript
// 将所有的
const cookieStore = cookies();

// 改为
const cookieStore = await cookies();
```

### 3. 使用自动部署脚本

```bash
# 检查环境变量
npm run check-env

# 预览部署
./scripts/deploy.sh preview

# 生产部署
./scripts/deploy.sh production
```

### 4. 手动部署（备选方案）

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署到预览环境
vercel

# 部署到生产环境
vercel --prod
```

## 🔧 新增功能

### 1. 扩展的假用户数据

新的 `scripts/seed-users-new.ts` 包含：
- **10个男性用户**：程序员、健身教练、创业者、医生、摄影师、建筑师、音乐制作人、律师、厨师、海洋工程师
- **20个女性用户**：文艺青年、设计师、心理咨询师、古典舞老师、海洋生物学家、瑜伽教练、护士、大学生、幼儿园老师、市场营销经理等

每个用户都有：
- 详细的个人资料
- 丰富的兴趣爱好
- 个性化的自我描述
- 明确的感情目标

### 2. 健康检查 API

新增 `/api/health` 端点，提供：
- 数据库连接状态
- 环境变量检查
- 服务状态监控
- 部署信息显示

### 3. 环境变量检查

`npm run check-env` 命令会：
- 检查所有必需的环境变量
- 显示已配置的变量（敏感信息会被遮蔽）
- 提供配置建议

### 4. 自动部署脚本

`./scripts/deploy.sh` 提供：
- 完整的部署前检查
- 自动化的部署流程
- 部署后的健康检查
- 友好的用户界面

## 📋 部署检查清单

使用 `DEPLOYMENT_CHECKLIST.md` 确保：

- [ ] 代码已修复 cookies API 警告
- [ ] 环境变量已正确配置
- [ ] Supabase 数据库已准备就绪
- [ ] 测试数据已生成
- [ ] 本地构建测试通过

## 🎯 部署后验证

部署完成后，访问以下 URL 验证：

```bash
# 应用主页
https://your-app.vercel.app

# 健康检查
https://your-app.vercel.app/api/health

# 登录页面
https://your-app.vercel.app/auth/login
```

## 🆘 常见问题

### 1. 构建失败
```bash
# 检查 TypeScript 错误
npm run type-check

# 检查 ESLint 错误
npm run lint
```

### 2. 环境变量问题
```bash
# 运行检查脚本
npm run check-env
```

### 3. 数据库连接问题
- 检查 Supabase URL 和密钥
- 确认 RLS 策略配置
- 验证网络访问权限

## 📊 性能优化

部署配置已包含：
- 函数超时优化（AI 生成 45s，匹配 60s）
- 区域设置（香港、新加坡）
- 图片优化配置
- 缓存策略设置

## 🔗 相关文档

- 📖 [详细部署文档](./DEPLOYMENT.md)
- ✅ [部署检查清单](./DEPLOYMENT_CHECKLIST.md)
- 🔧 [环境变量模板](./.env.example)

## 🎉 部署成功！

部署完成后，您将拥有一个功能完整的 AI 交友应用，包括：

- ✨ AI 智能个人资料生成
- 💕 智能匹配算法
- 💬 实时聊天功能
- 📱 响应式设计
- 🔒 安全的用户认证
- 📊 健康监控

**祝您部署顺利！** 🚀

---

> 💡 **提示**：首次部署建议先使用预览环境测试，确认一切正常后再部署到生产环境。
