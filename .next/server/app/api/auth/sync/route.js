/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/sync/route";
exports.ids = ["app/api/auth/sync/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsync%2Froute&page=%2Fapi%2Fauth%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsync%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsync%2Froute&page=%2Fapi%2Fauth%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsync%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_auth_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/sync/route.ts */ \"(rsc)/./src/app/api/auth/sync/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/sync/route\",\n        pathname: \"/api/auth/sync\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/sync/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/auth/sync/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_auth_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsync%2Froute&page=%2Fapi%2Fauth%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsync%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/sync/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/auth/sync/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_services_auth_sync__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/auth-sync */ \"(rsc)/./src/lib/services/auth-sync.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 同步用户到数据库\n        await _lib_services_auth_sync__WEBPACK_IMPORTED_MODULE_2__.AuthSyncService.syncUserToDatabase(user);\n        // 检查资料完整性\n        const isProfileComplete = await _lib_services_auth_sync__WEBPACK_IMPORTED_MODULE_2__.AuthSyncService.checkProfileCompleteness(user.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            isProfileComplete,\n            userId: user.id\n        });\n    } catch (error) {\n        console.error('Error in auth sync API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/sync/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiAgentFeedback),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   matches: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matches),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   userProfiles: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles),\n/* harmony export */   userSessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userSessions),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\n\n\n\n// Create the connection\nconst connectionString = process.env.DATABASE_URL;\nlet db;\nif (connectionString) {\n    // Create postgres client\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        max: 1,\n        idle_timeout: 20,\n        connect_timeout: 10\n    });\n    // Create drizzle instance\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n} else {\n    // Mock database for build time - create a minimal mock\n    const mockClient = {};\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(mockClient, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\n\n// Export schema for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsd0JBQXdCO0FBQ3hCLE1BQU1HLG1CQUFtQkMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBRWpELElBQUlDO0FBRUosSUFBSUosa0JBQWtCO0lBQ3BCLHlCQUF5QjtJQUN6QixNQUFNSyxTQUFTUCxvREFBUUEsQ0FBQ0Usa0JBQWtCO1FBQ3hDTSxLQUFLO1FBQ0xDLGNBQWM7UUFDZEMsaUJBQWlCO0lBQ25CO0lBRUEsMEJBQTBCO0lBQzFCSixLQUFLUCxnRUFBT0EsQ0FBQ1EsUUFBUTtRQUFFTixNQUFNQSxzQ0FBQUE7SUFBQztBQUNoQyxPQUFPO0lBQ0wsdURBQXVEO0lBQ3ZELE1BQU1VLGFBQWEsQ0FBQztJQUNwQkwsS0FBS1AsZ0VBQU9BLENBQUNZLFlBQVk7UUFBRVYsTUFBTUEsc0NBQUFBO0lBQUM7QUFDcEM7QUFFYztBQUVkLHVDQUF1QztBQUNkIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvZGIvaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZHJpenpsZSB9IGZyb20gJ2RyaXp6bGUtb3JtL3Bvc3RncmVzLWpzJztcbmltcG9ydCBwb3N0Z3JlcyBmcm9tICdwb3N0Z3Jlcyc7XG5pbXBvcnQgKiBhcyBzY2hlbWEgZnJvbSAnLi9zY2hlbWEnO1xuXG4vLyBDcmVhdGUgdGhlIGNvbm5lY3Rpb25cbmNvbnN0IGNvbm5lY3Rpb25TdHJpbmcgPSBwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkw7XG5cbmxldCBkYjogUmV0dXJuVHlwZTx0eXBlb2YgZHJpenpsZT47XG5cbmlmIChjb25uZWN0aW9uU3RyaW5nKSB7XG4gIC8vIENyZWF0ZSBwb3N0Z3JlcyBjbGllbnRcbiAgY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywge1xuICAgIG1heDogMSxcbiAgICBpZGxlX3RpbWVvdXQ6IDIwLFxuICAgIGNvbm5lY3RfdGltZW91dDogMTAsXG4gIH0pO1xuXG4gIC8vIENyZWF0ZSBkcml6emxlIGluc3RhbmNlXG4gIGRiID0gZHJpenpsZShjbGllbnQsIHsgc2NoZW1hIH0pO1xufSBlbHNlIHtcbiAgLy8gTW9jayBkYXRhYmFzZSBmb3IgYnVpbGQgdGltZSAtIGNyZWF0ZSBhIG1pbmltYWwgbW9ja1xuICBjb25zdCBtb2NrQ2xpZW50ID0ge30gYXMgYW55O1xuICBkYiA9IGRyaXp6bGUobW9ja0NsaWVudCwgeyBzY2hlbWEgfSk7XG59XG5cbmV4cG9ydCB7IGRiIH07XG5cbi8vIEV4cG9ydCBzY2hlbWEgZm9yIHVzZSBpbiBvdGhlciBmaWxlc1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiZGIiLCJjbGllbnQiLCJtYXgiLCJpZGxlX3RpbWVvdXQiLCJjb25uZWN0X3RpbWVvdXQiLCJtb2NrQ2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* binding */ aiAgentFeedback),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   userProfiles: () => (/* binding */ userProfiles),\n/* harmony export */   userSessions: () => (/* binding */ userSessions),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/uuid.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/jsonb.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n\n// Users table\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    avatar: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('avatar'),\n    bio: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('bio'),\n    age: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('age'),\n    gender: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gender'),\n    location: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('location'),\n    interests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('interests').$type().default([]),\n    personalityTraits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('personality_traits').$type(),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// User profiles table for additional profile information\nconst userProfiles = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_profiles', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    selfDescription: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('self_description'),\n    lookingFor: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('looking_for'),\n    relationshipGoals: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('relationship_goals'),\n    lifestyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('lifestyle').$type(),\n    values: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('values').$type().default([]),\n    photos: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('photos').$type().default([]),\n    preferences: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('preferences').$type(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Matches table\nconst matches = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('matches', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    user1Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user1_id').references(()=>users.id).notNull(),\n    user2Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user2_id').references(()=>users.id).notNull(),\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score'),\n    aiAnalysis: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('ai_analysis').$type(),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation').$type(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    user1Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_liked').default(false),\n    user2Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_liked').default(false),\n    user1Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_viewed').default(false),\n    user2Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_viewed').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// AI Agent Feedback table\nconst aiAgentFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('ai_agent_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    feedbackType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_type').notNull(),\n    feedbackText: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_text'),\n    rating: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rating'),\n    aspectRated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('aspect_rated'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// Conversations table for storing chat messages\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('conversation_id').references(()=>conversations.id).notNull(),\n    senderId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('sender_id').references(()=>users.id).notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('content').notNull(),\n    messageType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('message_type').default('text'),\n    isRead: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_read').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// User sessions for tracking activity\nconst userSessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_sessions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_token').notNull().unique(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('expires_at').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/auth-sync.ts":
/*!***************************************!*\
  !*** ./src/lib/services/auth-sync.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthSyncService: () => (/* binding */ AuthSyncService)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n\n\n\nclass AuthSyncService {\n    /**\n   * 同步 Supabase Auth 用户到我们的 users 表\n   * 这个函数会在用户首次登录或邮箱验证后调用\n   */ static async syncUserToDatabase(supabaseUser) {\n        try {\n            // 检查用户是否已经存在于我们的数据库中\n            const existingUser = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, supabaseUser.id)).limit(1);\n            if (existingUser.length > 0) {\n                // 用户已存在，更新最后登录时间\n                const [updatedUser] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).set({\n                    updatedAt: new Date(),\n                    email: supabaseUser.email || existingUser[0].email\n                }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, supabaseUser.id)).returning();\n                return updatedUser;\n            } else {\n                // 用户不存在，创建新用户记录\n                const newUserData = {\n                    id: supabaseUser.id,\n                    email: supabaseUser.email,\n                    name: supabaseUser.user_metadata?.name || null,\n                    avatar: supabaseUser.user_metadata?.avatar_url || null,\n                    isActive: true\n                };\n                const [newUser] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).values(newUserData).returning();\n                return newUser;\n            }\n        } catch (error) {\n            console.error('Error syncing user to database:', error);\n            throw error;\n        }\n    }\n    /**\n   * 获取完整的用户信息（包括 profile）\n   */ static async getCompleteUserInfo(userId) {\n        try {\n            const user = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId)).limit(1);\n            if (!user.length) {\n                return null;\n            }\n            return user[0];\n        } catch (error) {\n            console.error('Error getting complete user info:', error);\n            return null;\n        }\n    }\n    /**\n   * 检查用户是否需要完善资料\n   */ static async checkProfileCompleteness(userId) {\n        try {\n            const user = await this.getCompleteUserInfo(userId);\n            if (!user) return false;\n            // 检查基本信息是否完整\n            const hasBasicInfo = user.name && user.age && user.gender && user.location;\n            return !!hasBasicInfo;\n        } catch (error) {\n            console.error('Error checking profile completeness:', error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2F1dGgtc3luYy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThCO0FBQ3dCO0FBQ3JCO0FBRzFCLE1BQU1HO0lBQ1g7OztHQUdDLEdBQ0QsYUFBYUMsbUJBQW1CQyxZQUEwQixFQUFnQjtRQUN4RSxJQUFJO1lBQ0YscUJBQXFCO1lBQ3JCLE1BQU1DLGVBQWUsTUFBTU4sdUNBQUVBLENBQzFCTyxNQUFNLEdBQ05DLElBQUksQ0FBQ1AsaURBQUtBLEVBQ1ZRLEtBQUssQ0FBQ1AsK0NBQUVBLENBQUNELGlEQUFLQSxDQUFDUyxFQUFFLEVBQUVMLGFBQWFLLEVBQUUsR0FDbENDLEtBQUssQ0FBQztZQUVULElBQUlMLGFBQWFNLE1BQU0sR0FBRyxHQUFHO2dCQUMzQixpQkFBaUI7Z0JBQ2pCLE1BQU0sQ0FBQ0MsWUFBWSxHQUFHLE1BQU1iLHVDQUFFQSxDQUMzQmMsTUFBTSxDQUFDYixpREFBS0EsRUFDWmMsR0FBRyxDQUFDO29CQUNIQyxXQUFXLElBQUlDO29CQUNmQyxPQUFPYixhQUFhYSxLQUFLLElBQUlaLFlBQVksQ0FBQyxFQUFFLENBQUNZLEtBQUs7Z0JBQ3BELEdBQ0NULEtBQUssQ0FBQ1AsK0NBQUVBLENBQUNELGlEQUFLQSxDQUFDUyxFQUFFLEVBQUVMLGFBQWFLLEVBQUUsR0FDbENTLFNBQVM7Z0JBRVosT0FBT047WUFDVCxPQUFPO2dCQUNMLGdCQUFnQjtnQkFDaEIsTUFBTU8sY0FBdUI7b0JBQzNCVixJQUFJTCxhQUFhSyxFQUFFO29CQUNuQlEsT0FBT2IsYUFBYWEsS0FBSztvQkFDekJHLE1BQU1oQixhQUFhaUIsYUFBYSxFQUFFRCxRQUFRO29CQUMxQ0UsUUFBUWxCLGFBQWFpQixhQUFhLEVBQUVFLGNBQWM7b0JBQ2xEQyxVQUFVO2dCQUNaO2dCQUVBLE1BQU0sQ0FBQ0MsUUFBUSxHQUFHLE1BQU0xQix1Q0FBRUEsQ0FDdkIyQixNQUFNLENBQUMxQixpREFBS0EsRUFDWjJCLE1BQU0sQ0FBQ1IsYUFDUEQsU0FBUztnQkFFWixPQUFPTztZQUNUO1FBQ0YsRUFBRSxPQUFPRyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYUUsb0JBQW9CQyxNQUFjLEVBQUU7UUFDL0MsSUFBSTtZQUNGLE1BQU1DLE9BQU8sTUFBTWpDLHVDQUFFQSxDQUNsQk8sTUFBTSxHQUNOQyxJQUFJLENBQUNQLGlEQUFLQSxFQUNWUSxLQUFLLENBQUNQLCtDQUFFQSxDQUFDRCxpREFBS0EsQ0FBQ1MsRUFBRSxFQUFFc0IsU0FDbkJyQixLQUFLLENBQUM7WUFFVCxJQUFJLENBQUNzQixLQUFLckIsTUFBTSxFQUFFO2dCQUNoQixPQUFPO1lBQ1Q7WUFFQSxPQUFPcUIsSUFBSSxDQUFDLEVBQUU7UUFDaEIsRUFBRSxPQUFPSixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQ0FBcUNBO1lBQ25ELE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhSyx5QkFBeUJGLE1BQWMsRUFBb0I7UUFDdEUsSUFBSTtZQUNGLE1BQU1DLE9BQU8sTUFBTSxJQUFJLENBQUNGLG1CQUFtQixDQUFDQztZQUU1QyxJQUFJLENBQUNDLE1BQU0sT0FBTztZQUVsQixhQUFhO1lBQ2IsTUFBTUUsZUFBZUYsS0FBS1osSUFBSSxJQUFJWSxLQUFLRyxHQUFHLElBQUlILEtBQUtJLE1BQU0sSUFBSUosS0FBS0ssUUFBUTtZQUUxRSxPQUFPLENBQUMsQ0FBQ0g7UUFDWCxFQUFFLE9BQU9OLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdDQUF3Q0E7WUFDdEQsT0FBTztRQUNUO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiL2hvbWUvdWJ0MjIvd29ya3NwYWNlL2luZGllL2xpbmd4aWFpLWdlbWluaS9zcmMvbGliL3NlcnZpY2VzL2F1dGgtc3luYy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkYiB9IGZyb20gJ0AvbGliL2RiJztcbmltcG9ydCB7IHVzZXJzLCB0eXBlIE5ld1VzZXIgfSBmcm9tICdAL2xpYi9kYi9zY2hlbWEnO1xuaW1wb3J0IHsgZXEgfSBmcm9tICdkcml6emxlLW9ybSc7XG5pbXBvcnQgdHlwZSB7IFVzZXIgYXMgU3VwYWJhc2VVc2VyIH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuZXhwb3J0IGNsYXNzIEF1dGhTeW5jU2VydmljZSB7XG4gIC8qKlxuICAgKiDlkIzmraUgU3VwYWJhc2UgQXV0aCDnlKjmiLfliLDmiJHku6znmoQgdXNlcnMg6KGoXG4gICAqIOi/meS4quWHveaVsOS8muWcqOeUqOaIt+mmluasoeeZu+W9leaIlumCrueusemqjOivgeWQjuiwg+eUqFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHN5bmNVc2VyVG9EYXRhYmFzZShzdXBhYmFzZVVzZXI6IFN1cGFiYXNlVXNlcik6IFByb21pc2U8YW55PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIOajgOafpeeUqOaIt+aYr+WQpuW3sue7j+WtmOWcqOS6juaIkeS7rOeahOaVsOaNruW6k+S4rVxuICAgICAgY29uc3QgZXhpc3RpbmdVc2VyID0gYXdhaXQgZGJcbiAgICAgICAgLnNlbGVjdCgpXG4gICAgICAgIC5mcm9tKHVzZXJzKVxuICAgICAgICAud2hlcmUoZXEodXNlcnMuaWQsIHN1cGFiYXNlVXNlci5pZCkpXG4gICAgICAgIC5saW1pdCgxKTtcblxuICAgICAgaWYgKGV4aXN0aW5nVXNlci5sZW5ndGggPiAwKSB7XG4gICAgICAgIC8vIOeUqOaIt+W3suWtmOWcqO+8jOabtOaWsOacgOWQjueZu+W9leaXtumXtFxuICAgICAgICBjb25zdCBbdXBkYXRlZFVzZXJdID0gYXdhaXQgZGJcbiAgICAgICAgICAudXBkYXRlKHVzZXJzKVxuICAgICAgICAgIC5zZXQoeyBcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcbiAgICAgICAgICAgIGVtYWlsOiBzdXBhYmFzZVVzZXIuZW1haWwgfHwgZXhpc3RpbmdVc2VyWzBdLmVtYWlsLFxuICAgICAgICAgIH0pXG4gICAgICAgICAgLndoZXJlKGVxKHVzZXJzLmlkLCBzdXBhYmFzZVVzZXIuaWQpKVxuICAgICAgICAgIC5yZXR1cm5pbmcoKTtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiB1cGRhdGVkVXNlcjtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIOeUqOaIt+S4jeWtmOWcqO+8jOWIm+W7uuaWsOeUqOaIt+iusOW9lVxuICAgICAgICBjb25zdCBuZXdVc2VyRGF0YTogTmV3VXNlciA9IHtcbiAgICAgICAgICBpZDogc3VwYWJhc2VVc2VyLmlkLFxuICAgICAgICAgIGVtYWlsOiBzdXBhYmFzZVVzZXIuZW1haWwhLFxuICAgICAgICAgIG5hbWU6IHN1cGFiYXNlVXNlci51c2VyX21ldGFkYXRhPy5uYW1lIHx8IG51bGwsXG4gICAgICAgICAgYXZhdGFyOiBzdXBhYmFzZVVzZXIudXNlcl9tZXRhZGF0YT8uYXZhdGFyX3VybCB8fCBudWxsLFxuICAgICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IFtuZXdVc2VyXSA9IGF3YWl0IGRiXG4gICAgICAgICAgLmluc2VydCh1c2VycylcbiAgICAgICAgICAudmFsdWVzKG5ld1VzZXJEYXRhKVxuICAgICAgICAgIC5yZXR1cm5pbmcoKTtcblxuICAgICAgICByZXR1cm4gbmV3VXNlcjtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3luY2luZyB1c2VyIHRvIGRhdGFiYXNlOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDojrflj5blrozmlbTnmoTnlKjmiLfkv6Hmga/vvIjljIXmi6wgcHJvZmlsZe+8iVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldENvbXBsZXRlVXNlckluZm8odXNlcklkOiBzdHJpbmcpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlciA9IGF3YWl0IGRiXG4gICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAuZnJvbSh1c2VycylcbiAgICAgICAgLndoZXJlKGVxKHVzZXJzLmlkLCB1c2VySWQpKVxuICAgICAgICAubGltaXQoMSk7XG5cbiAgICAgIGlmICghdXNlci5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB1c2VyWzBdO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIGNvbXBsZXRlIHVzZXIgaW5mbzonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5qOA5p+l55So5oi35piv5ZCm6ZyA6KaB5a6M5ZaE6LWE5paZXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgY2hlY2tQcm9maWxlQ29tcGxldGVuZXNzKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBhd2FpdCB0aGlzLmdldENvbXBsZXRlVXNlckluZm8odXNlcklkKTtcbiAgICAgIFxuICAgICAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7XG5cbiAgICAgIC8vIOajgOafpeWfuuacrOS/oeaBr+aYr+WQpuWujOaVtFxuICAgICAgY29uc3QgaGFzQmFzaWNJbmZvID0gdXNlci5uYW1lICYmIHVzZXIuYWdlICYmIHVzZXIuZ2VuZGVyICYmIHVzZXIubG9jYXRpb247XG4gICAgICBcbiAgICAgIHJldHVybiAhIWhhc0Jhc2ljSW5mbztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgcHJvZmlsZSBjb21wbGV0ZW5lc3M6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbImRiIiwidXNlcnMiLCJlcSIsIkF1dGhTeW5jU2VydmljZSIsInN5bmNVc2VyVG9EYXRhYmFzZSIsInN1cGFiYXNlVXNlciIsImV4aXN0aW5nVXNlciIsInNlbGVjdCIsImZyb20iLCJ3aGVyZSIsImlkIiwibGltaXQiLCJsZW5ndGgiLCJ1cGRhdGVkVXNlciIsInVwZGF0ZSIsInNldCIsInVwZGF0ZWRBdCIsIkRhdGUiLCJlbWFpbCIsInJldHVybmluZyIsIm5ld1VzZXJEYXRhIiwibmFtZSIsInVzZXJfbWV0YWRhdGEiLCJhdmF0YXIiLCJhdmF0YXJfdXJsIiwiaXNBY3RpdmUiLCJuZXdVc2VyIiwiaW5zZXJ0IiwidmFsdWVzIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2V0Q29tcGxldGVVc2VySW5mbyIsInVzZXJJZCIsInVzZXIiLCJjaGVja1Byb2ZpbGVDb21wbGV0ZW5lc3MiLCJoYXNCYXNpY0luZm8iLCJhZ2UiLCJnZW5kZXIiLCJsb2NhdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/auth-sync.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createRouteClient: () => (/* binding */ createRouteClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nconst createClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\nconst createRouteClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n        cookies: ()=>cookieStore\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/drizzle-orm","vendor-chunks/postgres"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsync%2Froute&page=%2Fapi%2Fauth%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsync%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();