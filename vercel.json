{"version": 2, "framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/matches/route.ts": {"maxDuration": 60}, "app/api/profile/generate/route.ts": {"maxDuration": 45}}, "regions": ["sin1"], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/health", "destination": "/api/health"}]}