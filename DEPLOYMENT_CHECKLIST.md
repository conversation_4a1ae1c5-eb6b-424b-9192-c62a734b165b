# 🚀 部署前检查清单

在部署到 Vercel 之前，请确保完成以下所有检查项：

## 📋 代码准备

### 基础检查
- [ ] 所有代码已提交到 Git 仓库
- [ ] 没有 console.log 或调试代码残留
- [ ] 所有 TypeScript 错误已修复
- [ ] ESLint 检查通过 (`npm run lint`)
- [ ] 本地构建成功 (`npm run build`)

### 环境变量
- [ ] 复制 `.env.example` 到 `.env.local` 并填写正确的值
- [ ] 运行环境变量检查 (`npm run check-env`)
- [ ] 确认所有必需的环境变量都已设置

### 代码修复
- [ ] 修复所有 `cookies()` API 警告（添加 await）
- [ ] 检查所有 API 路由的错误处理
- [ ] 确认图片路径和静态资源正确

## 🗄️ 数据库准备

### Supabase 配置
- [ ] Supabase 项目已创建并配置
- [ ] 所有必要的表已创建：
  - [ ] `users` 表
  - [ ] `user_profiles` 表
  - [ ] `matches` 表
  - [ ] `conversations` 表
  - [ ] `messages` 表
- [ ] RLS (Row Level Security) 策略已正确配置
- [ ] Auth 设置已配置（邮箱确认、密码策略等）

### 测试数据
- [ ] 运行用户数据生成脚本 (`npm run db:seed`)
- [ ] 确认测试用户可以正常登录
- [ ] 验证匹配算法正常工作

## ⚙️ Vercel 配置

### 项目设置
- [ ] Vercel 账户已创建
- [ ] GitHub 仓库已连接到 Vercel
- [ ] 项目框架设置为 Next.js

### 环境变量配置
在 Vercel Dashboard 中设置以下环境变量：

#### 必需变量
- [ ] `NEXT_PUBLIC_SUPABASE_URL`
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- [ ] `SUPABASE_SERVICE_ROLE_KEY`
- [ ] `OPENROUTER_API_KEY`
- [ ] `NEXTAUTH_SECRET`
- [ ] `NEXTAUTH_URL`

#### 可选变量
- [ ] `NODE_ENV=production`
- [ ] `ANALYTICS_ID` (如果使用分析服务)

### 构建配置
- [ ] `vercel.json` 文件已创建并配置
- [ ] 函数超时时间已适当设置
- [ ] 区域设置已配置（推荐：hkg1, sin1）

## 🧪 功能测试

### 核心功能
- [ ] 用户注册流程正常
- [ ] 用户登录流程正常
- [ ] 个人资料编辑功能正常
- [ ] AI 生成功能正常工作
- [ ] 匹配系统正常运行
- [ ] 聊天功能正常

### API 测试
- [ ] 所有 API 端点响应正常
- [ ] 错误处理正确实现
- [ ] 健康检查端点工作正常 (`/api/health`)

### 性能测试
- [ ] 页面加载时间合理（< 3秒）
- [ ] 图片优化正常工作
- [ ] 移动端响应式设计正常

## 🔒 安全检查

### 数据安全
- [ ] 敏感信息不在客户端暴露
- [ ] API 路由有适当的身份验证
- [ ] Supabase RLS 策略正确配置
- [ ] 环境变量安全存储

### 访问控制
- [ ] 用户只能访问自己的数据
- [ ] 管理员功能有适当保护
- [ ] CORS 设置正确

## 📊 监控和分析

### 错误监控
- [ ] 错误边界已实现
- [ ] 日志记录已配置
- [ ] 健康检查端点已设置

### 性能监控
- [ ] Vercel Analytics 已启用
- [ ] Core Web Vitals 监控已设置

## 🚀 部署执行

### 部署前最后检查
- [ ] 运行完整的预部署检查：`npm run pre-deploy`
- [ ] 确认所有测试通过
- [ ] 代码已推送到主分支

### 部署步骤
1. [ ] 首次部署：`npm run deploy-preview` (预览环境)
2. [ ] 测试预览环境功能
3. [ ] 生产部署：`npm run deploy`
4. [ ] 验证生产环境功能

### 部署后验证
- [ ] 访问生产 URL 确认应用正常运行
- [ ] 测试关键用户流程
- [ ] 检查健康检查端点：`https://your-app.vercel.app/api/health`
- [ ] 验证所有环境变量正确加载
- [ ] 检查 Vercel 部署日志无错误

## 📝 文档更新

### 部署信息
- [ ] 更新 README.md 包含生产 URL
- [ ] 记录部署配置和环境变量
- [ ] 更新 API 文档（如果有）

### 维护信息
- [ ] 设置定期备份计划
- [ ] 配置监控告警
- [ ] 准备回滚计划

## ✅ 完成确认

- [ ] 所有检查项已完成
- [ ] 应用在生产环境正常运行
- [ ] 团队成员已通知部署完成
- [ ] 用户可以正常访问和使用应用

---

## 🆘 如果遇到问题

1. **构建失败**：检查 TypeScript 错误和依赖问题
2. **环境变量问题**：运行 `npm run check-env` 检查配置
3. **数据库连接问题**：验证 Supabase 配置和网络访问
4. **API 错误**：检查 Vercel 函数日志和超时设置
5. **性能问题**：检查图片优化和代码分割

## 📞 支持资源

- [Vercel 文档](https://vercel.com/docs)
- [Next.js 部署指南](https://nextjs.org/docs/deployment)
- [Supabase 文档](https://supabase.com/docs)
- 项目 DEPLOYMENT.md 文档

**记住**：部署是一个迭代过程，第一次可能不会完美。保持耐心，逐步解决问题！

🎉 **祝您部署成功！**
