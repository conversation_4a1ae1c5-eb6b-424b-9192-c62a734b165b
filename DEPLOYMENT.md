# 🚀 Vercel 部署指南

本文档详细说明了将 GuaLao AI 交友应用部署到 Vercel 的完整步骤和所需配置。

## 📋 部署前准备清单

### 1. 环境变量配置

在 Vercel 项目设置中需要配置以下环境变量：

#### 必需的环境变量
```bash
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://hgwftsuazcmgemuxxpue.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# OpenRouter API (用于 AI 功能)
OPENROUTER_API_KEY=sk-or-v1-...

# Next.js 配置
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=https://your-app-name.vercel.app
```

#### 可选的环境变量
```bash
# 开发环境标识
NODE_ENV=production

# 其他第三方服务
ANALYTICS_ID=your-analytics-id
```

### 2. 代码修改

#### 2.1 修复 Cookies API 警告

当前代码中有多处 `cookies()` 使用警告，需要修复：

**文件需要修改：**
- `src/app/api/auth/sync/route.ts`
- `src/app/api/profile/route.ts`
- `src/app/api/profile/generate/route.ts`
- `src/app/api/matches/route.ts`
- `src/app/api/matches/[id]/route.ts`

**修改方式：**
```typescript
// 修改前
const cookieStore = cookies();
const token = cookieStore.get('sb-hgwftsuazcmgemuxxpue-auth-token');

// 修改后
const cookieStore = await cookies();
const token = cookieStore.get('sb-hgwftsuazcmgemuxxpue-auth-token');
```

#### 2.2 环境变量引用

确保所有环境变量都正确引用：

```typescript
// 检查这些文件中的环境变量引用
- src/lib/supabase/client.ts
- src/lib/supabase/server.ts
- src/lib/services/gemini.ts
- scripts/seed-users.ts
```

#### 2.3 静态文件优化

确保所有静态资源都在 `public/` 目录下，并且路径正确。

### 3. 数据库准备

#### 3.1 Supabase 生产环境配置

1. **确认 Supabase 项目设置**
   - 确保 RLS (Row Level Security) 策略正确配置
   - 检查所有表的权限设置
   - 验证 Auth 配置

2. **生产数据准备**
   ```bash
   # 运行用户数据生成脚本
   npm run seed-users
   ```

#### 3.2 数据库迁移

确保所有必要的表和函数都已创建：
- `users` 表
- `user_profiles` 表  
- `matches` 表
- `conversations` 表
- `messages` 表

## 🔧 Vercel 配置

### 1. vercel.json 配置

创建 `vercel.json` 文件：

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "regions": ["hkg1", "sin1"],
  "env": {
    "NODE_ENV": "production"
  }
}
```

### 2. 构建优化

#### 2.1 Next.js 配置优化

在 `next.config.js` 中添加生产环境优化：

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 现有配置...
  
  // 生产环境优化
  compress: true,
  poweredByHeader: false,
  
  // 图片优化
  images: {
    domains: ['hgwftsuazcmgemuxxpue.supabase.co'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@supabase/supabase-js'],
  },
  
  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  }
}

module.exports = nextConfig
```

#### 2.2 依赖优化

检查 `package.json` 确保没有不必要的依赖：

```bash
# 清理未使用的依赖
npm prune

# 检查依赖安全性
npm audit

# 更新依赖到最新稳定版本
npm update
```

## 📦 部署步骤

### 1. 准备代码

```bash
# 1. 确保代码已提交到 Git
git add .
git commit -m "准备 Vercel 部署"
git push origin main

# 2. 本地测试构建
npm run build
npm run start

# 3. 测试生产环境
npm run lint
npm run type-check  # 如果有的话
```

### 2. Vercel 部署

#### 方式一：通过 Vercel CLI

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署
vercel --prod
```

#### 方式二：通过 Vercel Dashboard

1. 访问 [vercel.com](https://vercel.com)
2. 连接 GitHub 仓库
3. 配置环境变量
4. 点击部署

### 3. 环境变量配置

在 Vercel Dashboard 中设置环境变量：

1. 进入项目设置
2. 点击 "Environment Variables"
3. 添加所有必需的环境变量
4. 确保选择正确的环境 (Production/Preview/Development)

## 🔍 部署后检查

### 1. 功能测试清单

- [ ] 用户注册/登录
- [ ] 个人资料编辑
- [ ] AI 生成功能
- [ ] 匹配系统
- [ ] 聊天功能
- [ ] 图片上传
- [ ] 响应式设计

### 2. 性能检查

```bash
# 使用 Lighthouse 检查性能
npx lighthouse https://your-app.vercel.app

# 检查 Core Web Vitals
# 在 Vercel Analytics 中查看
```

### 3. 错误监控

设置错误监控：
- Vercel Analytics
- Sentry (可选)
- 自定义错误日志

## 🚨 常见问题解决

### 1. 构建失败

**问题：** TypeScript 类型错误
```bash
# 解决方案
npm run type-check
# 修复所有类型错误后重新部署
```

**问题：** 依赖安装失败
```bash
# 解决方案
rm -rf node_modules package-lock.json
npm install
```

### 2. 运行时错误

**问题：** 环境变量未定义
- 检查 Vercel 环境变量配置
- 确保变量名称完全匹配

**问题：** Supabase 连接失败
- 检查 Supabase URL 和密钥
- 确认 RLS 策略配置

### 3. 性能问题

**问题：** 页面加载慢
- 启用 Next.js 图片优化
- 使用动态导入减少包大小
- 配置适当的缓存策略

## 📊 监控和维护

### 1. 设置监控

```javascript
// 在 _app.tsx 中添加错误边界
import { ErrorBoundary } from 'react-error-boundary'

function ErrorFallback({error, resetErrorBoundary}) {
  return (
    <div role="alert">
      <h2>出错了:</h2>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>重试</button>
    </div>
  )
}

export default function App({ Component, pageProps }) {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <Component {...pageProps} />
    </ErrorBoundary>
  )
}
```

### 2. 日志记录

```typescript
// 添加结构化日志
const logger = {
  info: (message: string, data?: any) => {
    console.log(JSON.stringify({ level: 'info', message, data, timestamp: new Date().toISOString() }))
  },
  error: (message: string, error?: any) => {
    console.error(JSON.stringify({ level: 'error', message, error: error?.message, timestamp: new Date().toISOString() }))
  }
}
```

### 3. 定期维护

- 每周检查 Vercel Analytics
- 每月更新依赖
- 定期备份 Supabase 数据
- 监控 API 使用量和成本

## 🎯 优化建议

### 1. 性能优化

- 使用 Next.js Image 组件
- 实现代码分割
- 启用 gzip 压缩
- 配置 CDN 缓存

### 2. SEO 优化

- 添加适当的 meta 标签
- 实现结构化数据
- 优化页面标题和描述
- 添加 sitemap.xml

### 3. 用户体验

- 添加加载状态
- 实现错误处理
- 优化移动端体验
- 添加离线支持

---

## 📞 支持

如果在部署过程中遇到问题，可以：

1. 检查 Vercel 部署日志
2. 查看 Supabase 日志
3. 参考 Next.js 官方文档
4. 联系技术支持

**部署成功后，您的应用将在以下地址可用：**
`https://your-app-name.vercel.app`

🎉 祝您部署顺利！

## 📁 附录：配置文件模板

### A. vercel.json 完整配置

```json
{
  "version": 2,
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    },
    "app/api/matches/route.ts": {
      "maxDuration": 60
    },
    "app/api/profile/generate/route.ts": {
      "maxDuration": 45
    }
  },
  "regions": ["hkg1", "sin1"],
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/health",
      "destination": "/api/health"
    }
  ]
}
```

### B. 环境变量检查脚本

创建 `scripts/check-env.js`：

```javascript
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'OPENROUTER_API_KEY',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ 缺少以下环境变量:');
  missingVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  process.exit(1);
} else {
  console.log('✅ 所有必需的环境变量都已设置');
}
```

### C. 健康检查 API

创建 `src/app/api/health/route.ts`：

```typescript
import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // 检查数据库连接
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      throw error;
    }

    // 检查环境变量
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'OPENROUTER_API_KEY'
    ];

    const missingVars = requiredEnvVars.filter(
      varName => !process.env[varName]
    );

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      environment: process.env.NODE_ENV,
      missingEnvVars: missingVars,
      version: process.env.npm_package_version || '1.0.0'
    });

  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    }, { status: 500 });
  }
}
```

### D. 部署前检查脚本

在 `package.json` 中添加脚本：

```json
{
  "scripts": {
    "check-env": "node scripts/check-env.js",
    "pre-deploy": "npm run check-env && npm run lint && npm run build",
    "deploy": "vercel --prod",
    "deploy-preview": "vercel"
  }
}
```
