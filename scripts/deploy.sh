#!/bin/bash

# <PERSON>uaLao AI 部署脚本
# 使用方法: ./scripts/deploy.sh [preview|production]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查参数
DEPLOY_TYPE=${1:-preview}

if [[ "$DEPLOY_TYPE" != "preview" && "$DEPLOY_TYPE" != "production" ]]; then
    log_error "无效的部署类型。使用 'preview' 或 'production'"
    exit 1
fi

log_info "开始 $DEPLOY_TYPE 部署流程..."

# 1. 检查 Git 状态
log_info "检查 Git 状态..."
if [[ -n $(git status --porcelain) ]]; then
    log_warning "有未提交的更改，是否继续？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_error "部署已取消"
        exit 1
    fi
fi

# 2. 检查环境变量
log_info "检查环境变量..."
if ! npm run check-env; then
    log_error "环境变量检查失败"
    exit 1
fi
log_success "环境变量检查通过"

# 3. 运行 Lint 检查
log_info "运行代码检查..."
if ! npm run lint; then
    log_error "代码检查失败"
    exit 1
fi
log_success "代码检查通过"

# 4. 类型检查
log_info "运行类型检查..."
if ! npm run type-check; then
    log_error "TypeScript 类型检查失败"
    exit 1
fi
log_success "类型检查通过"

# 5. 构建测试
log_info "测试构建..."
if ! npm run build; then
    log_error "构建失败"
    exit 1
fi
log_success "构建测试通过"

# 6. 检查 Vercel CLI
if ! command -v vercel &> /dev/null; then
    log_warning "Vercel CLI 未安装，正在安装..."
    npm install -g vercel
fi

# 7. 执行部署
log_info "开始部署到 $DEPLOY_TYPE 环境..."

if [[ "$DEPLOY_TYPE" == "production" ]]; then
    log_warning "即将部署到生产环境，确认继续？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_error "部署已取消"
        exit 1
    fi
    
    vercel --prod
    DEPLOY_URL=$(vercel --prod 2>&1 | grep -o 'https://[^[:space:]]*' | tail -1)
else
    vercel
    DEPLOY_URL=$(vercel 2>&1 | grep -o 'https://[^[:space:]]*' | tail -1)
fi

# 8. 部署后检查
if [[ -n "$DEPLOY_URL" ]]; then
    log_success "部署完成！"
    log_info "部署 URL: $DEPLOY_URL"
    
    # 等待部署完成
    log_info "等待部署完成..."
    sleep 10
    
    # 健康检查
    log_info "执行健康检查..."
    if curl -f "$DEPLOY_URL/api/health" > /dev/null 2>&1; then
        log_success "健康检查通过"
    else
        log_warning "健康检查失败，请手动验证"
    fi
    
    # 打开浏览器
    if command -v open &> /dev/null; then
        log_info "在浏览器中打开应用..."
        open "$DEPLOY_URL"
    elif command -v xdg-open &> /dev/null; then
        log_info "在浏览器中打开应用..."
        xdg-open "$DEPLOY_URL"
    fi
    
else
    log_error "无法获取部署 URL"
    exit 1
fi

# 9. 部署总结
echo ""
log_success "🎉 部署完成！"
echo ""
echo "📊 部署信息:"
echo "   类型: $DEPLOY_TYPE"
echo "   URL: $DEPLOY_URL"
echo "   时间: $(date)"
echo ""
echo "🔗 有用的链接:"
echo "   应用: $DEPLOY_URL"
echo "   健康检查: $DEPLOY_URL/api/health"
echo "   Vercel Dashboard: https://vercel.com/dashboard"
echo ""

if [[ "$DEPLOY_TYPE" == "production" ]]; then
    echo "🎯 生产环境部署完成！"
    echo "   请进行完整的功能测试"
    echo "   监控应用性能和错误"
else
    echo "🧪 预览环境部署完成！"
    echo "   测试完成后可以部署到生产环境："
    echo "   ./scripts/deploy.sh production"
fi

echo ""
log_success "部署脚本执行完成！"
