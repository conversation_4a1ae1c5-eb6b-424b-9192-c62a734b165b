#!/usr/bin/env node

const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'OPENROUTER_API_KEY',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];

const optionalEnvVars = [
  'NODE_ENV',
  'ANALYTICS_ID'
];

console.log('🔍 检查环境变量配置...\n');

// 检查必需的环境变量
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
const presentVars = requiredEnvVars.filter(varName => process.env[varName]);

// 检查可选的环境变量
const presentOptionalVars = optionalEnvVars.filter(varName => process.env[varName]);

// 显示结果
console.log('✅ 已配置的必需环境变量:');
presentVars.forEach(varName => {
  const value = process.env[varName];
  const maskedValue = varName.includes('KEY') || varName.includes('SECRET') 
    ? `${value.substring(0, 8)}...` 
    : value;
  console.log(`  ✓ ${varName}: ${maskedValue}`);
});

if (presentOptionalVars.length > 0) {
  console.log('\n📋 已配置的可选环境变量:');
  presentOptionalVars.forEach(varName => {
    console.log(`  ✓ ${varName}: ${process.env[varName]}`);
  });
}

if (missingVars.length > 0) {
  console.log('\n❌ 缺少以下必需环境变量:');
  missingVars.forEach(varName => {
    console.error(`  ✗ ${varName}`);
  });
  
  console.log('\n💡 请在 Vercel Dashboard 或 .env.local 文件中设置这些变量');
  console.log('   参考 DEPLOYMENT.md 文档获取详细说明');
  
  process.exit(1);
} else {
  console.log('\n🎉 所有必需的环境变量都已正确配置！');
  
  // 额外检查
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  
  if (supabaseUrl && !supabaseUrl.startsWith('https://')) {
    console.warn('\n⚠️  警告: NEXT_PUBLIC_SUPABASE_URL 应该以 https:// 开头');
  }
  
  if (nextAuthUrl && !nextAuthUrl.startsWith('https://') && !nextAuthUrl.startsWith('http://localhost')) {
    console.warn('\n⚠️  警告: NEXTAUTH_URL 应该是完整的 URL (包含 https://)');
  }
  
  console.log('\n✨ 环境变量检查完成，可以继续部署！');
}
