# 寡佬AI 设置指南

## 🚀 快速开始

### 1. 环境配置

首先，您需要配置以下环境变量。复制 `.env.local.example` 为 `.env.local` 并填入真实值：

```bash
cp .env.local.example .env.local
```

在 `.env.local` 中配置：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key

# 数据库连接
DATABASE_URL=your_postgresql_connection_string
```

### 2. 获取 Supabase 配置

1. 访问 [Supabase](https://supabase.com) 并创建新项目
2. 在项目设置中找到 API 配置：
   - `NEXT_PUBLIC_SUPABASE_URL`: 项目 URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: anon public key
   - `SUPABASE_SERVICE_ROLE_KEY`: service_role key (用于管理员操作)
3. 在数据库设置中获取连接字符串作为 `DATABASE_URL`

### 3. 获取 Google Gemini API Key

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的 API Key
3. 将 API Key 设置为 `GEMINI_API_KEY`

### 4. 数据库迁移

```bash
# 生成迁移文件
npm run db:generate

# 执行迁移（如果需要）
npm run db:migrate
```

### 5. 生成假用户数据

运行种子脚本来创建10个丰富的假用户：

```bash
npm run db:seed
```

这将创建以下用户：
- 小雨 (<EMAIL>) - 文艺青年，喜欢阅读旅行
- 晓明 (<EMAIL>) - 程序员，热爱技术
- 梦琪 (<EMAIL>) - 设计师，追求美好生活
- 浩然 (<EMAIL>) - 健身教练，热爱运动
- 雨婷 (<EMAIL>) - 心理咨询师，善于倾听
- 志伟 (<EMAIL>) - 创业者，经营咖啡店
- 思思 (<EMAIL>) - 古典舞老师，热爱传统文化
- 俊豪 (<EMAIL>) - 医生，有责任心
- 小涵 (<EMAIL>) - 海洋生物学研究生
- 天宇 (<EMAIL>) - 摄影师，艺术创作者

所有用户的密码都是：`password123`

## 🎯 使用流程

### 1. 注册新用户
- 访问 `/auth/register` 注册新账户
- 检查邮箱并点击验证链接
- 系统会自动引导您完善个人资料

### 2. 完善个人资料
- 填写详细的个人信息
- 添加兴趣爱好标签
- 写好自我描述和理想伴侣要求

### 3. 生成AI匹配
- 在 Dashboard 点击"🤖 生成AI匹配"
- AI 会分析您的资料并生成匹配推荐
- 每个匹配都包含详细的兼容性分析

### 4. 查看匹配详情
- 点击"查看完整AI分析"进入详情页
- 观看AI模拟的对话场景
- 查看详细的匹配分析报告
- 提供反馈帮助AI改进

## 🔧 开发功能

### API 端点

- `GET /api/matches` - 获取用户匹配列表
- `GET /api/matches?generate_daily=true` - 生成每日匹配
- `GET /api/matches/[id]` - 获取单个匹配详情
- `PATCH /api/matches/[id]` - 更新匹配状态（喜欢/跳过）
- `GET /api/profile` - 获取用户资料
- `PUT /api/profile` - 更新用户资料
- `POST /api/auth/sync` - 同步用户认证状态
- `POST /api/feedback` - 提交AI反馈

### 数据库命令

```bash
# 查看数据库
npm run db:studio

# 重新生成种子数据
npm run db:seed

# 生成新的迁移
npm run db:generate
```

## 🎨 功能特性

### AI 匹配引擎
- 基于 Google Gemini 的深度人格分析
- 多维度兼容性评估
- 智能对话模拟
- 持续学习和优化

### 用户体验
- 渐进式对话展示
- 精美的卡片设计
- 流畅的动画效果
- 响应式布局

### 隐私保护
- 双向确认机制
- 安全的用户认证
- 数据加密存储

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 是否正确
   - 确保数据库服务正在运行

2. **Supabase 认证失败**
   - 验证 Supabase 配置是否正确
   - 检查 API keys 是否有效

3. **AI 分析失败**
   - 确认 `GEMINI_API_KEY` 是否设置
   - 检查 API 配额是否充足

4. **种子数据创建失败**
   - 确保有 `SUPABASE_SERVICE_ROLE_KEY`
   - 检查数据库表是否已创建

### 重置数据

如果需要重置所有数据：

```bash
# 删除所有用户数据（谨慎操作）
# 可以在 Supabase Dashboard 中手动删除表数据
# 然后重新运行种子脚本
npm run db:seed
```

## 📞 支持

如果遇到问题，请检查：
1. 环境变量配置是否正确
2. 数据库连接是否正常
3. API keys 是否有效
4. 控制台是否有错误信息

---

🎉 现在您可以开始体验完整的AI匹配功能了！
