# Supabase 配置
# 从 Supabase Dashboard > Settings > API 获取
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# OpenRouter API 配置
# 从 https://openrouter.ai/keys 获取
OPENROUTER_API_KEY=sk-or-v1-...

# NextAuth 配置
# 生成随机字符串: openssl rand -base64 32
NEXTAUTH_SECRET=your-nextauth-secret-here
# 生产环境 URL
NEXTAUTH_URL=https://your-app-name.vercel.app

# 可选配置
NODE_ENV=production
ANALYTICS_ID=your-analytics-id

# 开发环境配置 (仅本地使用)
# NODE_ENV=development
# NEXTAUTH_URL=http://localhost:3001
