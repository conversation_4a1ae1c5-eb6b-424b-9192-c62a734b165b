# 寡佬AI - 智能恋爱匹配平台

基于AI技术的智能恋爱匹配平台，通过深度分析性格特征和价值观，为用户精准匹配最合适的伴侣。

## 🌟 核心功能

### AI深度分析
- 基于Google Gemini AI技术
- 深度分析用户性格特征、价值观和生活方式
- 生成详细的人格画像和匹配报告

### 智能匹配算法
- 综合考虑性格互补、价值观一致、兴趣重叠等多个维度
- 科学计算兼容性分数
- AI模拟对话预测沟通效果

### 隐私保护
- 双向确认机制
- 只有互相喜欢才能开始聊天
- 严格的用户验证和隐私保护

### 持续学习
- AI根据用户反馈不断优化
- 提供越来越准确的匹配建议

## 🛠 技术栈

- **前端**: Next.js 15, React 18, TypeScript
- **样式**: Tailwind CSS, Radix UI
- **后端**: Next.js API Routes
- **数据库**: PostgreSQL + Drizzle ORM
- **认证**: Supabase Auth
- **AI**: Google Gemini API
- **状态管理**: Zustand

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   ├── auth/              # 认证页面
│   ├── dashboard/         # 主控制台
│   ├── profile/           # 用户资料
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── ui/               # 基础UI组件
│   ├── MatchCard.tsx     # 匹配卡片
│   └── ConversationModal.tsx # 对话模拟弹窗
├── lib/                  # 工具库
│   ├── db/               # 数据库配置
│   ├── services/         # 业务逻辑
│   ├── supabase/         # Supabase 客户端
│   └── utils.ts          # 工具函数
└── types/                # TypeScript 类型定义
```

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd gualaoai-gemini
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制环境变量模板：
```bash
cp .env.local.example .env.local
```

配置以下环境变量：
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key

# Database
DATABASE_URL=your_database_connection_string
```

### 4. 数据库设置
```bash
# 生成数据库迁移
npm run db:generate

# 执行迁移
npm run db:migrate
```

### 5. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📊 数据库设计

### 核心表结构
- `users` - 用户基本信息
- `user_profiles` - 用户详细资料
- `matches` - 匹配记录
- `ai_agent_feedback` - AI反馈数据
- `conversations` - 对话记录
- `messages` - 消息记录

## 🔧 开发命令

```bash
# 开发
npm run dev

# 构建
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# 数据库相关
npm run db:generate    # 生成迁移文件
npm run db:migrate     # 执行迁移
npm run db:studio      # 打开数据库管理界面
```

## 🎯 核心业务流程

### 1. 用户注册和资料完善
- 邮箱注册验证
- 详细填写个人信息和偏好
- AI生成人格摘要

### 2. AI匹配流程
- 分析用户人格特征
- 寻找潜在匹配对象
- 计算兼容性分数
- 模拟对话场景

### 3. 匹配互动
- 查看匹配推荐
- 双向确认机制
- 成功匹配后开始聊天

### 4. 反馈优化
- 用户对AI分析的反馈
- 持续优化匹配算法

## 🔒 安全特性

- Supabase 认证和授权
- 环境变量保护敏感信息
- API 路由权限验证
- 用户数据加密存储

## 📈 未来规划

- [ ] 实时聊天功能
- [ ] 视频通话集成
- [ ] 移动端应用
- [ ] 高级匹配算法
- [ ] 社交功能扩展
- [ ] 多语言支持

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目链接: [https://github.com/yourusername/gualaoai-gemini](https://github.com/yourusername/gualaoai-gemini)
- 问题反馈: [Issues](https://github.com/yourusername/gualaoai-gemini/issues)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
